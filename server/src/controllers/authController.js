import { User } from '../models/User.js';
import { Admin } from '../models/Admin.js';
import { hashPassword, verifyPassword } from '../utils/password.js';
import { generateToken } from '../utils/jwt.js';
import { 
  successResponse, 
  errorResponse, 
  conflictResponse, 
  unauthorizedResponse,
  validationErrorResponse 
} from '../utils/response.js';

/**
 * 用户注册
 */
export async function registerUser(req, res) {
  try {
    const { username, email, password, phone_number } = req.body;

    // 检查邮箱是否已存在
    const emailExists = await User.emailExists(email);
    if (emailExists) {
      return conflictResponse(res, '该邮箱已被注册');
    }

    // 检查用户名是否已存在
    const usernameExists = await User.usernameExists(username);
    if (usernameExists) {
      return conflictResponse(res, '该用户名已被使用');
    }

    // 加密密码
    const password_hash = await hashPassword(password);

    // 创建用户
    const newUser = await User.create({
      username,
      email,
      password_hash,
      phone_number: phone_number || null
    });

    // 生成JWT token
    const token = generateToken({
      id: newUser.id,
      username: newUser.username,
      email: newUser.email,
      isAdmin: false
    });

    return successResponse(res, {
      user: newUser.toSafeJSON(),
      token
    }, '注册成功', 201);

  } catch (error) {
    console.error('用户注册失败:', error);
    return errorResponse(res, '注册失败，请稍后重试', 500);
  }
}

/**
 * 用户登录
 */
export async function loginUser(req, res) {
  try {
    const { email, password } = req.body;

    // 查找用户（包含密码哈希）
    const user = await User.findByEmailWithPassword(email);
    if (!user) {
      return unauthorizedResponse(res, '邮箱或密码错误');
    }

    // 验证密码
    const isPasswordValid = await verifyPassword(password, user.password_hash);
    if (!isPasswordValid) {
      return unauthorizedResponse(res, '邮箱或密码错误');
    }

    // 生成JWT token
    const token = generateToken({
      id: user.id,
      username: user.username,
      email: user.email,
      isAdmin: false
    });

    // 创建安全的用户对象（不包含密码）
    const safeUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      phone_number: user.phone_number,
      created_at: user.created_at,
      updated_at: user.updated_at
    };

    return successResponse(res, {
      user: safeUser,
      token
    }, '登录成功');

  } catch (error) {
    console.error('用户登录失败:', error);
    return errorResponse(res, '登录失败，请稍后重试', 500);
  }
}

/**
 * 管理员登录
 */
export async function loginAdmin(req, res) {
  try {
    const { username, password } = req.body;

    // 查找管理员（包含密码哈希）
    const admin = await Admin.findByUsernameWithPassword(username);
    if (!admin) {
      return unauthorizedResponse(res, '用户名或密码错误');
    }

    // 验证密码
    const isPasswordValid = await verifyPassword(password, admin.password_hash);
    if (!isPasswordValid) {
      return unauthorizedResponse(res, '用户名或密码错误');
    }

    // 生成JWT token
    const token = generateToken({
      id: admin.id,
      username: admin.username,
      role: admin.role,
      isAdmin: true
    });

    // 创建安全的管理员对象（不包含密码）
    const safeAdmin = {
      id: admin.id,
      username: admin.username,
      role: admin.role,
      created_at: admin.created_at
    };

    return successResponse(res, {
      admin: safeAdmin,
      token
    }, '管理员登录成功');

  } catch (error) {
    console.error('管理员登录失败:', error);
    return errorResponse(res, '登录失败，请稍后重试', 500);
  }
}

/**
 * 获取当前用户信息
 */
export async function getCurrentUser(req, res) {
  try {
    const userId = req.user.id;
    
    const user = await User.findById(userId);
    if (!user) {
      return unauthorizedResponse(res, '用户不存在');
    }

    return successResponse(res, user.toSafeJSON(), '获取用户信息成功');

  } catch (error) {
    console.error('获取用户信息失败:', error);
    return errorResponse(res, '获取用户信息失败', 500);
  }
}

/**
 * 获取当前管理员信息
 */
export async function getCurrentAdmin(req, res) {
  try {
    const adminId = req.admin.id;
    
    const admin = await Admin.findById(adminId);
    if (!admin) {
      return unauthorizedResponse(res, '管理员不存在');
    }

    return successResponse(res, admin.toSafeJSON(), '获取管理员信息成功');

  } catch (error) {
    console.error('获取管理员信息失败:', error);
    return errorResponse(res, '获取管理员信息失败', 500);
  }
}

/**
 * 修改用户密码
 */
export async function changeUserPassword(req, res) {
  try {
    const userId = req.user.id;
    const { currentPassword, newPassword } = req.body;

    // 获取用户当前密码哈希
    const user = await User.findByEmailWithPassword(req.user.email);
    if (!user) {
      return unauthorizedResponse(res, '用户不存在');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await verifyPassword(currentPassword, user.password_hash);
    if (!isCurrentPasswordValid) {
      return unauthorizedResponse(res, '当前密码错误');
    }

    // 加密新密码
    const newPasswordHash = await hashPassword(newPassword);

    // 更新密码
    const success = await User.updatePassword(userId, newPasswordHash);
    if (!success) {
      return errorResponse(res, '密码更新失败', 500);
    }

    return successResponse(res, null, '密码修改成功');

  } catch (error) {
    console.error('修改密码失败:', error);
    return errorResponse(res, '密码修改失败，请稍后重试', 500);
  }
}

/**
 * 修改管理员密码
 */
export async function changeAdminPassword(req, res) {
  try {
    const adminId = req.admin.id;
    const { currentPassword, newPassword } = req.body;

    // 获取管理员当前密码哈希
    const admin = await Admin.findByUsernameWithPassword(req.admin.username);
    if (!admin) {
      return unauthorizedResponse(res, '管理员不存在');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await verifyPassword(currentPassword, admin.password_hash);
    if (!isCurrentPasswordValid) {
      return unauthorizedResponse(res, '当前密码错误');
    }

    // 加密新密码
    const newPasswordHash = await hashPassword(newPassword);

    // 更新密码
    const success = await Admin.updatePassword(adminId, newPasswordHash);
    if (!success) {
      return errorResponse(res, '密码更新失败', 500);
    }

    return successResponse(res, null, '密码修改成功');

  } catch (error) {
    console.error('修改管理员密码失败:', error);
    return errorResponse(res, '密码修改失败，请稍后重试', 500);
  }
}

/**
 * 刷新token
 */
export async function refreshToken(req, res) {
  try {
    const user = req.user;

    // 生成新的token
    const newToken = generateToken({
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      isAdmin: user.isAdmin || false
    });

    return successResponse(res, { token: newToken }, 'Token刷新成功');

  } catch (error) {
    console.error('Token刷新失败:', error);
    return errorResponse(res, 'Token刷新失败', 500);
  }
}
