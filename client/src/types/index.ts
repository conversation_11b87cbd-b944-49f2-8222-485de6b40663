// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  phone_number?: string
  created_at: string
  updated_at: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  username: string
  email: string
  password: string
  phone_number?: string
}

export interface AuthResponse {
  success: boolean
  message: string
  data?: {
    user: User
    token: string
  }
}

// 宠物相关类型
export interface Pet {
  id: number
  user_id: number
  name: string
  species: string
  breed?: string
  color: string
  gender: 'male' | 'female' | 'unknown'
  age?: number
  description?: string
  photo_url?: string
  created_at: string
}

export interface PetFormData {
  name: string
  species: string
  breed?: string
  color: string
  gender: 'male' | 'female' | 'unknown'
  age?: number
  description?: string
  photo?: File | null
}

// 帖子相关类型
export interface Post {
  id: number
  user_id: number
  pet_id: number
  last_seen_location: string
  last_seen_time: string
  post_status: 'searching' | 'found' | 'closed'
  admin_status: 'pending' | 'approved' | 'rejected'
  video_url?: string
  contact_info?: string
  created_at: string
  updated_at: string
  pet?: Pet
  owner?: User
  sightings_count?: number
}

export interface PostFormData {
  pet_id: number
  last_seen_location: string
  last_seen_time: string
  video_url?: string
  contact_info?: string
}

// 线索相关类型
export interface Sighting {
  id: number
  post_id: number
  sighting_location: string
  sighting_time: string
  sighting_photo_url?: string
  description?: string
  is_verified: boolean
  created_at: string
  post_info?: {
    title: string
    pet_name: string
    pet_species: string
    pet_color: string
    owner_username: string
  }
}

export interface SightingFormData {
  post_id: number
  sighting_location: string
  sighting_time: string
  description?: string
  photo?: File | null
}

// 地图相关类型
export interface MapLocation {
  lat: number
  lng: number
  address?: string
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

export interface PaginatedResponse<T> {
  success: boolean
  message: string
  data?: {
    items: T[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
    }
  }
}

// 搜索和筛选类型
export interface SearchFilters {
  species?: string
  color?: string
  gender?: string
  location?: string
  dateFrom?: string
  dateTo?: string
  keyword?: string
}

export interface PaginationParams {
  page: number
  limit: number
}
