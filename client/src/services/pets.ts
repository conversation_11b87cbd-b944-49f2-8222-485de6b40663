import { apiRequest } from '@/utils/api'
import type {
  Pet,
  PetFormData,
  ApiResponse,
  PaginationParams,
  SearchFilters
} from '@/types'

/**
 * 宠物相关API服务
 */
export const petService = {
  /**
   * 创建宠物信息
   */
  create: async (data: PetFormData): Promise<ApiResponse<Pet>> => {
    const formData = new FormData()

    // 添加宠物基本信息
    formData.append('name', data.name)
    formData.append('species', data.species)
    formData.append('color', data.color)
    formData.append('gender', data.gender)

    if (data.breed) formData.append('breed', data.breed)
    if (data.age) formData.append('age', data.age.toString())
    if (data.description) formData.append('description', data.description)
    if (data.photo) formData.append('photo', data.photo)

    return await apiRequest.upload('/pets', formData)
  },

  /**
   * 获取我的宠物列表
   */
  getMyPets: async (params: PaginationParams): Promise<ApiResponse<Pet[]> & { pagination?: any }> => {
    const queryParams = new URLSearchParams({
      page: params.page.toString(),
      limit: params.limit.toString(),
    })

    return await apiRequest.get(`/pets/my?${queryParams}`) as ApiResponse<Pet[]> & { pagination?: any }
  },

  /**
   * 根据ID获取宠物详情
   */
  getById: async (id: number): Promise<ApiResponse<Pet>> => {
    return await apiRequest.get(`/pets/${id}`)
  },

  /**
   * 更新宠物信息
   */
  update: async (id: number, data: PetFormData): Promise<ApiResponse<Pet>> => {
    const formData = new FormData()

    // 添加宠物基本信息
    formData.append('name', data.name)
    formData.append('species', data.species)
    formData.append('color', data.color)
    formData.append('gender', data.gender)

    if (data.breed) formData.append('breed', data.breed)
    if (data.age) formData.append('age', data.age.toString())
    if (data.description) formData.append('description', data.description)
    if (data.photo) formData.append('photo', data.photo)

    return await apiRequest.upload(`/pets/${id}`, formData)
  },

  /**
   * 删除宠物
   */
  delete: async (id: number): Promise<ApiResponse> => {
    return await apiRequest.delete(`/pets/${id}`)
  },

  /**
   * 搜索宠物
   */
  search: async (filters: SearchFilters, params: PaginationParams): Promise<ApiResponse<Pet[]> & { pagination?: any }> => {
    const queryParams = new URLSearchParams({
      page: params.page.toString(),
      limit: params.limit.toString(),
    })

    if (filters.species) queryParams.append('species', filters.species)
    if (filters.color) queryParams.append('color', filters.color)
    if (filters.gender) queryParams.append('gender', filters.gender)
    if (filters.location) queryParams.append('location', filters.location)
    if (filters.keyword) queryParams.append('keyword', filters.keyword)

    return await apiRequest.get(`/pets/search?${queryParams}`) as ApiResponse<Pet[]> & { pagination?: any }
  },

  /**
   * 上传宠物照片
   */
  uploadPhoto: async (photo: File): Promise<ApiResponse<{ photo_url: string }>> => {
    const formData = new FormData()
    formData.append('photo', photo)

    return await apiRequest.upload('/pets/upload', formData)
  },
}
