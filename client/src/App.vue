<script setup lang="ts">
import { RouterView } from 'vue-router'
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import MobileNav from '@/components/MobileNav.vue'

const authStore = useAuthStore()

onMounted(() => {
  // 初始化认证状态
  authStore.initAuth()
})
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 主要内容 -->
    <div class="pb-16 sm:pb-0">
      <RouterView />
    </div>

    <!-- 移动端底部导航 -->
    <MobileNav />
  </div>
</template>

<style scoped>
</style>
