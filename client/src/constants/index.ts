// 宠物品种选项
export const PET_SPECIES = [
  { value: '狗', label: '狗' },
  { value: '猫', label: '猫' },
  { value: '兔子', label: '兔子' },
  { value: '鸟类', label: '鸟类' },
  { value: '仓鼠', label: '仓鼠' },
  { value: '其他', label: '其他' },
]

// 宠物性别选项
export const PET_GENDERS = [
  { value: 'male', label: '雄性' },
  { value: 'female', label: '雌性' },
  { value: 'unknown', label: '不确定' },
]

// 宠物毛色选项
export const PET_COLORS = [
  { value: '黑色', label: '黑色' },
  { value: '白色', label: '白色' },
  { value: '棕色', label: '棕色' },
  { value: '金色', label: '金色' },
  { value: '灰色', label: '灰色' },
  { value: '花色', label: '花色' },
  { value: '其他', label: '其他' },
]

// 香港地区选项
export const HK_DISTRICTS = [
  { value: '中西区', label: '中西区' },
  { value: '湾仔区', label: '湾仔区' },
  { value: '东区', label: '东区' },
  { value: '南区', label: '南区' },
  { value: '油尖旺区', label: '油尖旺区' },
  { value: '深水埗区', label: '深水埗区' },
  { value: '九龙城区', label: '九龙城区' },
  { value: '黄大仙区', label: '黄大仙区' },
  { value: '观塘区', label: '观塘区' },
  { value: '荃湾区', label: '荃湾区' },
  { value: '屯门区', label: '屯门区' },
  { value: '元朗区', label: '元朗区' },
  { value: '北区', label: '北区' },
  { value: '大埔区', label: '大埔区' },
  { value: '沙田区', label: '沙田区' },
  { value: '西贡区', label: '西贡区' },
  { value: '葵青区', label: '葵青区' },
  { value: '离岛区', label: '离岛区' },
]

// 帖子状态
export const POST_STATUS = {
  SEARCHING: 'searching',
  FOUND: 'found',
  CLOSED: 'closed',
} as const

export const POST_STATUS_LABELS = {
  [POST_STATUS.SEARCHING]: '寻找中',
  [POST_STATUS.FOUND]: '已找到',
  [POST_STATUS.CLOSED]: '已关闭',
}

// 审核状态
export const ADMIN_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
} as const

export const ADMIN_STATUS_LABELS = {
  [ADMIN_STATUS.PENDING]: '待审核',
  [ADMIN_STATUS.APPROVED]: '已通过',
  [ADMIN_STATUS.REJECTED]: '已拒绝',
}

// 分页配置
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 12,
  MAX_LIMIT: 50,
}

// 文件上传配置
export const FILE_UPLOAD = {
  MAX_SIZE_MB: 5,
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/ogg'],
}

// 地图配置
export const MAP_CONFIG = {
  DEFAULT_CENTER: [22.3193, 114.1694], // 香港中心坐标
  DEFAULT_ZOOM: 11,
  MAX_ZOOM: 18,
  MIN_ZOOM: 8,
}

// 社交分享平台
export const SHARE_PLATFORMS = [
  {
    name: 'WhatsApp',
    icon: 'whatsapp',
    color: '#25D366',
    getUrl: (text: string, url: string) => `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`,
  },
  {
    name: 'Facebook',
    icon: 'facebook',
    color: '#1877F2',
    getUrl: (text: string, url: string) => `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
  },
  {
    name: 'Twitter',
    icon: 'twitter',
    color: '#1DA1F2',
    getUrl: (text: string, url: string) => `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`,
  },
  {
    name: 'Telegram',
    icon: 'telegram',
    color: '#0088CC',
    getUrl: (text: string, url: string) => `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`,
  },
]

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  SERVER_ERROR: '服务器错误，请稍后重试',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '没有权限执行此操作',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '输入信息有误，请检查后重试',
  FILE_TOO_LARGE: '文件大小超过限制',
  INVALID_FILE_TYPE: '不支持的文件类型',
}

// 成功消息
export const SUCCESS_MESSAGES = {
  LOGIN_SUCCESS: '登录成功',
  REGISTER_SUCCESS: '注册成功',
  LOGOUT_SUCCESS: '登出成功',
  CREATE_SUCCESS: '创建成功',
  UPDATE_SUCCESS: '更新成功',
  DELETE_SUCCESS: '删除成功',
  UPLOAD_SUCCESS: '上传成功',
  COPY_SUCCESS: '复制成功',
}
