<template>
  <div class="space-y-4">
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-2">
        {{ label }}
      </label>
      
      <!-- 地址输入框 -->
      <div class="mb-4">
        <input
          v-model="addressInput"
          type="text"
          :placeholder="placeholder"
          class="input-field"
          @keyup.enter="searchAddress"
        />
        <button
          type="button"
          @click="searchAddress"
          class="mt-2 btn-secondary text-sm"
        >
          搜索地址
        </button>
      </div>
      
      <!-- 地图容器 -->
      <div
        ref="mapContainer"
        class="w-full h-64 rounded-lg border border-gray-300"
      ></div>
      
      <!-- 选中的位置信息 -->
      <div v-if="selectedLocation" class="mt-2 p-3 bg-gray-50 rounded-lg">
        <p class="text-sm text-gray-600">
          <strong>选中位置：</strong>
        </p>
        <p class="text-sm text-gray-800">
          纬度: {{ selectedLocation.lat.toFixed(6) }}, 
          经度: {{ selectedLocation.lng.toFixed(6) }}
        </p>
        <p v-if="selectedLocation.address" class="text-sm text-gray-800">
          地址: {{ selectedLocation.address }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import L from 'leaflet'
import type { MapLocation } from '@/types'
import { MAP_CONFIG } from '@/constants'

// 修复Leaflet图标问题
delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
})

interface Props {
  modelValue?: MapLocation | null
  label?: string
  placeholder?: string
  required?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: MapLocation | null): void
}

const props = withDefaults(defineProps<Props>(), {
  label: '选择位置',
  placeholder: '输入地址搜索...',
  required: false,
})

const emit = defineEmits<Emits>()

const mapContainer = ref<HTMLElement>()
const addressInput = ref('')
const selectedLocation = ref<MapLocation | null>(null)

let map: L.Map | null = null
let marker: L.Marker | null = null

// 初始化地图
const initMap = () => {
  if (!mapContainer.value) return
  
  // 创建地图实例
  map = L.map(mapContainer.value).setView(
    MAP_CONFIG.DEFAULT_CENTER as [number, number], 
    MAP_CONFIG.DEFAULT_ZOOM
  )
  
  // 添加地图图层
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© OpenStreetMap contributors',
    maxZoom: MAP_CONFIG.MAX_ZOOM,
    minZoom: MAP_CONFIG.MIN_ZOOM,
  }).addTo(map)
  
  // 地图点击事件
  map.on('click', (e: L.LeafletMouseEvent) => {
    const { lat, lng } = e.latlng
    setLocation({ lat, lng })
  })
  
  // 如果有初始值，设置标记
  if (props.modelValue) {
    setLocation(props.modelValue)
  }
}

// 设置位置
const setLocation = (location: MapLocation) => {
  if (!map) return
  
  selectedLocation.value = location
  
  // 移除旧标记
  if (marker) {
    map.removeLayer(marker)
  }
  
  // 添加新标记
  marker = L.marker([location.lat, location.lng]).addTo(map)
  
  // 移动地图视图到选中位置
  map.setView([location.lat, location.lng], 15)
  
  // 尝试获取地址信息（反向地理编码）
  reverseGeocode(location.lat, location.lng)
  
  // 触发更新事件
  emit('update:modelValue', selectedLocation.value)
}

// 搜索地址
const searchAddress = async () => {
  if (!addressInput.value.trim()) return
  
  try {
    // 使用Nominatim API进行地理编码
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(
        addressInput.value + ', Hong Kong'
      )}&limit=1`
    )
    
    const data = await response.json()
    
    if (data && data.length > 0) {
      const result = data[0]
      const location: MapLocation = {
        lat: parseFloat(result.lat),
        lng: parseFloat(result.lon),
        address: result.display_name,
      }
      
      setLocation(location)
    } else {
      alert('未找到该地址，请尝试其他关键词')
    }
  } catch (error) {
    console.error('地址搜索失败:', error)
    alert('地址搜索失败，请稍后重试')
  }
}

// 反向地理编码
const reverseGeocode = async (lat: number, lng: number) => {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`
    )
    
    const data = await response.json()
    
    if (data && data.display_name) {
      if (selectedLocation.value) {
        selectedLocation.value.address = data.display_name
        emit('update:modelValue', selectedLocation.value)
      }
    }
  } catch (error) {
    console.error('反向地理编码失败:', error)
  }
}

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && map) {
      setLocation(newValue)
    }
  },
  { deep: true }
)

onMounted(() => {
  initMap()
})

onUnmounted(() => {
  if (map) {
    map.remove()
    map = null
  }
})
</script>
