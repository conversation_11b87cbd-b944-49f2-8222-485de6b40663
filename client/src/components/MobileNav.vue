<template>
  <nav class="mobile-nav sm:hidden">
    <div class="flex justify-around">
      <router-link
        to="/"
        :class="[
          'mobile-nav-item',
          $route.path === '/' ? 'active' : ''
        ]"
      >
        <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
        <span>首页</span>
      </router-link>

      <router-link
        to="/posts"
        :class="[
          'mobile-nav-item',
          $route.path === '/posts' ? 'active' : ''
        ]"
      >
        <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
        <span>浏览</span>
      </router-link>

      <router-link
        v-if="authStore.isAuthenticated"
        to="/post/create"
        :class="[
          'mobile-nav-item',
          $route.path === '/post/create' ? 'active' : ''
        ]"
      >
        <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        <span>发布</span>
      </router-link>

      <router-link
        v-if="authStore.isAuthenticated"
        to="/dashboard"
        :class="[
          'mobile-nav-item',
          $route.path.startsWith('/dashboard') ? 'active' : ''
        ]"
      >
        <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
        <span>我的</span>
      </router-link>

      <router-link
        v-else
        to="/login"
        :class="[
          'mobile-nav-item',
          $route.path === '/login' ? 'active' : ''
        ]"
      >
        <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
        </svg>
        <span>登录</span>
      </router-link>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()
</script>
