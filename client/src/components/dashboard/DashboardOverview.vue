<template>
  <div class="space-y-6">
    <!-- 欢迎信息 -->
    <div class="bg-white rounded-lg shadow p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-2">
        欢迎回来，{{ authStore.user?.username }}！
      </h2>
      <p class="text-gray-600">
        感谢您使用走失宠物协寻平台，让我们一起帮助更多宠物回家。
      </p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.5-.9-6.1-2.3" />
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">
                我的帖子
              </dt>
              <dd class="text-lg font-medium text-gray-900">
                {{ stats.posts || 0 }}
              </dd>
            </dl>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">
                我的宠物
              </dt>
              <dd class="text-lg font-medium text-gray-900">
                {{ stats.pets || 0 }}
              </dd>
            </dl>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">
                收到线索
              </dt>
              <dd class="text-lg font-medium text-gray-900">
                {{ stats.sightings || 0 }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">最近活动</h3>
      </div>
      <div class="p-6">
        <div v-if="loading" class="flex justify-center py-8">
          <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
        </div>
        <div v-else-if="recentPosts.length > 0" class="space-y-4">
          <div
            v-for="post in recentPosts"
            :key="post.id"
            class="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            <img
              v-if="post.pet?.photo_url"
              :src="getFullImageUrl(post.pet.photo_url)"
              :alt="post.pet.name"
              class="w-12 h-12 object-cover rounded-lg"
            />
            <div
              v-else
              class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center"
            >
              <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ post.pet?.name }}
              </p>
              <p class="text-sm text-gray-500">
                {{ formatDate(post.created_at) }}
              </p>
            </div>
            <span
              :class="[
                'px-2 py-1 text-xs font-medium rounded-full',
                post.post_status === 'searching'
                  ? 'bg-yellow-100 text-yellow-800'
                  : post.post_status === 'found'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800'
              ]"
            >
              {{ POST_STATUS_LABELS[post.post_status] }}
            </span>
          </div>
        </div>
        <div v-else class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.5-.9-6.1-2.3" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">暂无帖子</h3>
          <p class="mt-1 text-sm text-gray-500">
            您还没有发布任何寻宠信息
          </p>
          <div class="mt-6">
            <router-link
              to="/post/create"
              class="btn-primary"
            >
              发布走失信息
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速操作 -->
    <div class="bg-white rounded-lg shadow p-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <router-link
          to="/post/create"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 transition-colors"
        >
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-primary-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-900">发布走失信息</p>
            <p class="text-sm text-gray-500">快速发布宠物走失信息</p>
          </div>
        </router-link>

        <router-link
          to="/posts"
          class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 transition-colors"
        >
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-900">浏览寻宠信息</p>
            <p class="text-sm text-gray-500">查看其他人的寻宠信息</p>
          </div>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { postService } from '@/services/posts'
import { formatDate, getFullImageUrl } from '@/utils/helpers'
import type { Post } from '@/types'
import { POST_STATUS_LABELS, PAGINATION } from '@/constants'

const authStore = useAuthStore()

// 状态
const loading = ref(false)
const stats = ref({
  posts: 0,
  pets: 0,
  sightings: 0,
})
const recentPosts = ref<Post[]>([])

// 方法
const loadDashboardData = async () => {
  try {
    loading.value = true

    // 加载最近的帖子
    const response = await postService.getMyPosts({
      page: 1,
      limit: 5,
    })

    if (response.success && response.data) {
      // 服务器返回的格式是 { success, message, data: [...], pagination: {...} }
      recentPosts.value = response.data as Post[]
      // 从响应中获取分页信息
      if ('pagination' in response) {
        stats.value.posts = (response as any).pagination.totalItems
      }
    }
  } catch (error) {
    console.error('加载Dashboard数据失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadDashboardData()
})
</script>
