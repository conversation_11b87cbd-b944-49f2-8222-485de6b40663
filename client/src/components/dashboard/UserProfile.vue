<template>
  <div class="space-y-6">
    <!-- 基本信息 -->
    <div class="bg-white rounded-lg shadow p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">基本信息</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label class="block text-sm font-medium text-gray-700">用户名</label>
          <p class="mt-1 text-sm text-gray-900">{{ authStore.user?.username }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">邮箱</label>
          <p class="mt-1 text-sm text-gray-900">{{ authStore.user?.email }}</p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">手机号</label>
          <p class="mt-1 text-sm text-gray-900">
            {{ authStore.user?.phone_number || '未设置' }}
          </p>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700">注册时间</label>
          <p class="mt-1 text-sm text-gray-900">
            {{ formatDate(authStore.user?.created_at || '') }}
          </p>
        </div>
      </div>
    </div>

    <!-- 修改密码 -->
    <div class="bg-white rounded-lg shadow p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">修改密码</h2>
      
      <form @submit.prevent="handleChangePassword" class="space-y-4">
        <div>
          <label for="currentPassword" class="block text-sm font-medium text-gray-700">
            当前密码
          </label>
          <input
            id="currentPassword"
            v-model="passwordForm.currentPassword"
            type="password"
            required
            class="mt-1 input-field"
            placeholder="请输入当前密码"
          />
        </div>

        <div>
          <label for="newPassword" class="block text-sm font-medium text-gray-700">
            新密码
          </label>
          <input
            id="newPassword"
            v-model="passwordForm.newPassword"
            type="password"
            required
            minlength="6"
            class="mt-1 input-field"
            placeholder="请输入新密码（至少6位）"
          />
        </div>

        <div>
          <label for="confirmPassword" class="block text-sm font-medium text-gray-700">
            确认新密码
          </label>
          <input
            id="confirmPassword"
            v-model="passwordForm.confirmPassword"
            type="password"
            required
            class="mt-1 input-field"
            placeholder="请再次输入新密码"
          />
        </div>

        <!-- 错误提示 -->
        <div v-if="error" class="rounded-md bg-red-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                {{ error }}
              </h3>
            </div>
          </div>
        </div>

        <!-- 成功提示 -->
        <div v-if="success" class="rounded-md bg-green-50 p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-green-800">
                密码修改成功！
              </h3>
            </div>
          </div>
        </div>

        <div class="flex justify-end">
          <button
            type="submit"
            :disabled="loading"
            class="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="loading" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              修改中...
            </span>
            <span v-else>修改密码</span>
          </button>
        </div>
      </form>
    </div>

    <!-- 账户统计 -->
    <div class="bg-white rounded-lg shadow p-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">账户统计</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-primary-600">0</div>
          <div class="text-sm text-gray-500">发布的帖子</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">0</div>
          <div class="text-sm text-gray-500">收到的线索</div>
        </div>
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">0</div>
          <div class="text-sm text-gray-500">找回的宠物</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'
import { formatDate } from '@/utils/helpers'

const authStore = useAuthStore()

// 状态
const loading = ref(false)
const error = ref('')
const success = ref(false)

// 表单数据
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
})

// 计算属性
const isPasswordValid = computed(() => {
  return passwordForm.value.newPassword.length >= 6 &&
         passwordForm.value.newPassword === passwordForm.value.confirmPassword
})

// 方法
const handleChangePassword = async () => {
  error.value = ''
  success.value = false
  
  // 验证表单
  if (!passwordForm.value.currentPassword) {
    error.value = '请输入当前密码'
    return
  }
  
  if (passwordForm.value.newPassword.length < 6) {
    error.value = '新密码至少需要6个字符'
    return
  }
  
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    error.value = '两次输入的密码不一致'
    return
  }
  
  try {
    loading.value = true
    
    const result = await authStore.changePassword(
      passwordForm.value.currentPassword,
      passwordForm.value.newPassword
    )
    
    if (result) {
      success.value = true
      // 清空表单
      passwordForm.value = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      }
      
      // 3秒后隐藏成功提示
      setTimeout(() => {
        success.value = false
      }, 3000)
    } else {
      error.value = authStore.error || '修改密码失败'
    }
  } catch (err: any) {
    error.value = err.message || '修改密码失败，请稍后重试'
  } finally {
    loading.value = false
  }
}
</script>
