<template>
  <div class="bg-white rounded-lg shadow p-6">
    <div class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">我的宠物</h3>
      <p class="mt-1 text-sm text-gray-500">
        此功能正在开发中，敬请期待
      </p>
      <p class="mt-2 text-sm text-gray-500">
        目前您可以通过发布走失信息来添加宠物信息
      </p>
      <div class="mt-6">
        <router-link
          to="/post/create"
          class="btn-primary"
        >
          发布走失信息
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这个组件目前是占位符，后续可以扩展为完整的宠物管理功能
</script>
