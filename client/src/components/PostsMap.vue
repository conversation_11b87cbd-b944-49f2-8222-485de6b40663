<template>
  <div class="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
    <div v-if="loading" class="text-center">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
      <p class="mt-2 text-sm text-gray-600">加载地图中...</p>
    </div>
    <div v-else class="text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">地图视图</h3>
      <p class="mt-1 text-sm text-gray-500">
        地图功能正在开发中，敬请期待
      </p>
      <p class="mt-1 text-sm text-gray-500">
        找到 {{ posts.length }} 个走失信息
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Post } from '@/types'

interface Props {
  posts: Post[]
  loading: boolean
}

interface Emits {
  (e: 'post-select', postId: number): void
}

defineProps<Props>()
defineEmits<Emits>()
</script>
